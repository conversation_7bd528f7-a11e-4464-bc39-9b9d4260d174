# Enhanced Stellar OCR Tool v2.0

**🚀 Significantly improved version with direct window clicking, visual area selection, and emergency controls!**

## ✨ Key Enhancements

### 🎯 **Direct Window Clicking**
- **No mouse movement** - clicks sent directly to game window via pywinauto
- **Background operation** - works even when game not focused

### 🖱️ **Visual Area Selection**
- **Drag interface** - intuitive click-and-drag selection
- **Real-time preview** - see exactly what you're selecting
- **ESC to cancel** - easy to retry if you make a mistake

### 🚨 **Emergency Kill Switch**
- **Press ESC anytime** - instantly stops all automation
- **Global hotkey** - works even when window not focused
- **True panic button** - immediate control when needed

## 🔧 Installation

### Prerequisites
```bash
pip install pyautogui pytesseract pillow pywinauto mouse keyboard
```

### Tesseract OCR
- Download and install Tesseract OCR
- Place in `./Tesseract/` folder or ensure it's in system PATH

## 🎮 Quick Start

### 1. **Set Imprint Button** (One-time setup)
- Click "Set Imprint Button"
- Click on the Imprint button in your game
- Coordinates are captured automatically

### 2. **Define OCR Area**
- Click "Define area"
- Drag to select the text area you want to monitor
- Red rectangle shows your selection in real-time

### 3. **Configure Options**
- Select the stat you're looking for from dropdown
- Set minimum value (optional)
- Adjust ping/timing if needed

### 4. **Start Automation**
- Click "Start"
- Automation begins after 3-second countdown
- **Press ESC anytime to stop immediately**

## 📊 Available Options

- PVE Penetration
- PVE Critical DMG
- All Attack UP
- Penetration
- Critical DMG.
- **Ignore Accuracy** *(newly added)*

## 🛡️ Safety Features

### Emergency Controls
- **ESC Key** - Global emergency stop
- **Stop Button** - Normal stop via UI
- **Window Close** - Clean shutdown with hotkey removal

### Monitoring
- **Console Output** - Real-time OCR text display
- **Wrong Read Counter** - Monitor OCR accuracy
- **Log Files** - Detailed operation logs in `~/stellarlink_logs/`

## ⚙️ Advanced Features

### Timing Configuration
- **Ping Compensation** - Adjust for your system latency
- **OCR Timing** - Balanced for speed and accuracy
- **Click Intervals** - Optimized for game responsiveness

### Window Management
- **Automatic Connection** - Finds game window automatically
- **Coordinate Adjustment** - Handles window borders and title bars
- **Background Operation** - No need to keep game focused

## 🐛 Troubleshooting

### Common Issues
1. **"Could not connect to game"**
   - Ensure game is running
   - Check if game uses D3D Window class

2. **OCR not reading correctly**
   - Redefine area with better text visibility
   - Check console output for what OCR is seeing
   - Adjust area to include only the text you want

3. **Clicks not working**
   - Re-set Imprint button coordinates
   - Ensure game window hasn't moved
   - Check if game is in fullscreen vs windowed mode

### Debug Information
- **Console Output** - Shows real-time OCR results
- **Log Files** - Detailed operation history
- **Wrong Read Counter** - Indicates OCR quality

## 🔄 Changelog

See [CHANGELOG.md](CHANGELOG.md) for detailed list of improvements and changes.

## 📝 License

Same as original project.

## 🤝 Contributing

This is an enhanced fork of the original Stellar OCR tool. Feel free to:
- Report issues
- Suggest improvements
- Submit pull requests
- Fork for your own modifications

## ⚠️ Disclaimer

Use responsibly and in accordance with game terms of service. This tool is for educational and convenience purposes.
